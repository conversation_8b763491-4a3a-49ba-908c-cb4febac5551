package za.co.wethinkcode.robots.AcceptanceTests;

import com.fasterxml.jackson.databind.JsonNode;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import za.co.wethinkcode.robots.client.RobotWorldClient;
import za.co.wethinkcode.robots.client.RobotWorldJsonClient;
import za.co.wethinkcode.robots.server.ConfigLoader;
import za.co.wethinkcode.robots.server.World;
import za.co.wethinkcode.robots.server.Obstacle;
import za.co.wethinkcode.robots.server.ObstacleType;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

public class LaunchRobot2x2Test {
    private final static int DEFAULT_PORT = 5000;
    private final static String DEFAULT_IP = "localhost";
    private final RobotWorldClient serverClient = new RobotWorldJsonClient();
    private final ConfigLoader configLoader = new ConfigLoader();
    private String url;
    private Process serverProcess;

    @BeforeEach
    void connectToServer() throws IOException {
        startServer();
        serverClient.connect(DEFAULT_IP, DEFAULT_PORT);
    }

    @AfterEach
    void disconnectFromServer() {
        serverClient.disconnect();

        // Kill the server process
        if (serverProcess != null && serverProcess.isAlive()) {
            serverProcess.destroyForcibly();
            try {
                serverProcess.waitFor();
                Thread.sleep(500); // Wait for cleanup
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }

    // Helper methods to reduce duplication
    private void startServer(String... additionalArgs) throws IOException {
        // Load server url from properties file
        url = configLoader.loadServerUrl("serverConfig.properties");

        // Build server command with additional arguments
        List<String> command = new ArrayList<>();
        command.add("java");
        command.add("-jar");
        command.add(url);
        command.add("-s");
        command.add("2");
        command.addAll(Arrays.asList(additionalArgs));

        ProcessBuilder pb = new ProcessBuilder(command);
        serverProcess = pb.start(); // Store the process reference
        try {
            Thread.sleep(1000); // Wait for server to start
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    @Test
    @Order(5)
    void canLaunchAnotherRobotInLargerWorld() {
        // Given that I am connected to a Robot Worlds server
        // And the world is of size 2x2
        assertTrue(serverClient.isConnected());

        // And robot "HAL" has already been launched into the world
        String launchHAL = "{" +
                "\"robot\": \"HAL\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}";
        JsonNode responseHAL = serverClient.sendRequest(launchHAL);
        assertEquals("OK", responseHAL.get("result").asText());

        // When I launch the robot "R2D2" into the world
        String launchR2D2 = "{" +
                "\"robot\": \"R2D2\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}";
        JsonNode responseR2D2 = serverClient.sendRequest(launchR2D2);

        // Then the launch should be successful
        assertEquals("OK", responseR2D2.get("result").asText());

        // And a position should be returned
        assertNotNull(responseR2D2.get("data"));
        assertNotNull(responseR2D2.get("data").get("position"));
        assertTrue(responseR2D2.get("data").get("position").isArray());
        assertEquals(2, responseR2D2.get("data").get("position").size());
    }

    @Test
    @Order(6)
    void worldWithoutObstaclesIsFull() {
        // Given that I am connected to a Robot Worlds server
        // And the world is of size 2x2
        assertTrue(serverClient.isConnected());

        // And I have successfully launched 9 robots into the world
        for (int i = 1; i <= 9; i++) {
            String launchRequest = "{" +
                    "\"robot\": \"Robot" + i + "\"," +
                    "\"command\": \"launch\"," +
                    "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                    "}";
            JsonNode response = serverClient.sendRequest(launchRequest);
            assertEquals("OK", response.get("result").asText());
        }

        // When I try to launch one more robot
        String request = "{" +
                "\"robot\": \"ExtraRobot\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}";
        JsonNode response = serverClient.sendRequest(request);

        // Then I should get an error response
        assertNotNull(response.get("result"));
        assertEquals("ERROR", response.get("result").asText());

        // And the message should indicate no more space
        assertNotNull(response.get("data"));
        assertNotNull(response.get("data").get("message"));
        assertEquals("No more space in this world", response.get("data").get("message").asText());
    }

    @Nested
    class WorldWithObstacleTests {

        @BeforeEach
        void setupObstacleWorld() throws IOException {
            // Disconnect from current server if connected
            if (serverClient.isConnected()) {
                serverClient.disconnect();
            }

            // Kill existing server process
            if (serverProcess != null && serverProcess.isAlive()) {
                serverProcess.destroyForcibly();
                try {
                    serverProcess.waitFor();
                    Thread.sleep(500);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }

            // Start server with obstacle configuration
            startServer("-o", "1,1");
            serverClient.connect(DEFAULT_IP, DEFAULT_PORT);
        }

        @Test
        @Order(7)
        void worldWithObstacle() {
            // Given a world of size 2x2 with obstacle at [1,1]
            assertTrue(serverClient.isConnected());

            // When launching 8 robots (all available positions except obstacle)
            for (int i = 0; i < 8; i++) {
                String initialRequest = "{" +
                        "\"robot\": \"RoboCop" + i + "\"," +
                        "\"command\": \"launch\"," +
                        "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                        "}";
                JsonNode initialResponse = serverClient.sendRequest(initialRequest);
                assertEquals("OK", initialResponse.get("result").asText());

                // Then each robot cannot be in position [1,1]
                JsonNode initialPosition = initialResponse.get("data").get("position");
                assertFalse(initialPosition.get(0).asInt() == 1 && initialPosition.get(1).asInt() == 1);
            }

            // When trying to launch 9th robot (PAL) - should fail as world is full
            String request = "{" +
                    "\"robot\": \"PAL\"," +
                    "\"command\": \"launch\"," +
                    "\"arguments\": [\"sniper\",\"5\",\"5\"]" +
                    "}";

            JsonNode response = serverClient.sendRequest(request);

            // Then the launch should fail
            assertNotNull(response.get("result"));
            assertEquals("ERROR", response.get("result").asText());
        }

        @Test
        @Order(8)
        void worldWithObstacleIsFull() {
            // Given a world of size 2x2 with obstacle at [1,1]
            assertTrue(serverClient.isConnected());

            // And I have successfully launched 8 robots into the world (filling all available positions)
            for (int i = 0; i < 8; i++) {
                String initialRequest = "{" +
                        "\"robot\": \"Terminator" + i + "\"," +
                        "\"command\": \"launch\"," +
                        "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                        "}";
                JsonNode initialResponse = serverClient.sendRequest(initialRequest);
                assertEquals("OK", initialResponse.get("result").asText());

                // Verify robot is not placed at obstacle position [1,1]
                JsonNode position = initialResponse.get("data").get("position");
                assertFalse(position.get(0).asInt() == 1 && position.get(1).asInt() == 1);
            }

            // When I try to launch one more robot
            String request = "{" +
                    "\"robot\": \"T1000\"," +
                    "\"command\": \"launch\"," +
                    "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                    "}";
            JsonNode response = serverClient.sendRequest(request);

            // Then I should get an error response back with the message "No more space in this world"
            assertNotNull(response.get("result"));
            assertEquals("ERROR", response.get("result").asText());
            assertNotNull(response.get("data"));
            assertNotNull(response.get("data").get("message"));
            assertEquals("No more space in this world", response.get("data").get("message").asText());
        }
    }
}